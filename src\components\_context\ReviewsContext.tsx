"use client"

import React, { createContext, useState } from 'react';
import { getReviews, markReviewAsRead } from '@/services/api';

interface Review {
  id: string;
  public_review: string;
  reviewer_role: string;
  submitted_at: string;
  is_read?: boolean;
  [key: string]: any; // For other properties in the review object
}

interface ReviewsContextType {
    reviews: Review[];
    isLoadingReviews: boolean;
    fetchReviews: () => Promise<void>;
    markAsRead: (reviewId: string) => Promise<boolean>;
}

export const ReviewsContext = createContext<ReviewsContextType>({
    reviews: [],
    isLoadingReviews: true,
    fetchReviews: async () => {},
    markAsRead: async () => false,
});

export const ReviewsProvider = ({ children }: { children: React.ReactNode }) => {
    const [reviews, setReviews] = useState<Review[]>([]);
    const [isLoadingReviews, setIsLoadingReviews] = useState<boolean>(false);

    const fetchReviews = async () => {
        setIsLoadingReviews(true);
        try {
            const data = await getReviews();
            setReviews(data);
        } catch (error) {
            console.error('Error fetching reviews:', error);
        }
        setIsLoadingReviews(false);
    };

    const markAsRead = async (reviewId: string) => {
        try {
            const response = await markReviewAsRead(reviewId);
            if (response.status === 200 && response.data.is_read) {
                // Update the local state to reflect the change
                setReviews(prev => 
                    prev.map(review => 
                        review.id === reviewId ? { ...review, is_read: true } : review
                    )
                );
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error marking review as read:', error);
            return false;
        }
    };

    return (
        <ReviewsContext.Provider value={{ reviews, isLoadingReviews, fetchReviews, markAsRead }}>
            {children}
        </ReviewsContext.Provider>
    );
};
