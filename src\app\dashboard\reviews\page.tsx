'use client'
import React, { useContext, useEffect } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import { ReviewsContext } from '@/components/_context/ReviewsContext'
import CardReview from '@/components/cards/CardReview'
import Loader1 from '@/components/loaders/Loader1'
import { useRouter } from 'next/navigation'

function ReviewsPage() {
    const { reviews, isLoadingReviews, fetchReviews, markAsRead, markAllAsRead } = useContext(ReviewsContext)
    const router = useRouter()

    useEffect(() => {
        fetchReviews()
    }, [])

    const handleMarkAsRead = async (reviewId: string) => {
        await markAsRead(reviewId);
    };

    const handleMarkAllAsRead = async () => {
        const success = await markAllAsRead();
        if (success) {
            // Optional: Show success toast
            console.log('All reviews marked as read');
        }
    };

    const hasUnreadReviews = reviews.some(review => !review.is_read);
    const unreadCount = reviews.filter(r => !r.is_read).length;

    return (
        <MobilePageStart isNavbar>
            <HeaderPage
                title='Recensioni'
                actionLeftIcon={() => {
                    router.back()
                }}  
                actionRightIcon={()=> {
                    router.push('/dashboard')
                }}
            />
            <div className="px-4 py-6">                
                {isLoadingReviews ? (
                    <div className='h-full w-full flex items-center justify-center'>
                        <Loader1/>
                    </div>
                ) : reviews.length > 0 ? (
                    <div className="space-y-4">
                        {hasUnreadReviews && (
                            <div className="mb-6 p-4 rounded-lg" style={{ backgroundColor: '#f8f9fa', border: '1px solid #e9ecef' }}>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium" style={{ color: '#113158' }}>
                                            Hai {unreadCount} nuove recensioni da leggere
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                            Segna tutte come lette per rimuovere le notifiche
                                        </p>
                                    </div>
                                    <button
                                        onClick={handleMarkAllAsRead}
                                        className="text-xs text-white py-2 px-4 rounded-md transition-colors hover:opacity-90 whitespace-nowrap"
                                        style={{ backgroundColor: '#fcbd4c', color: '#113158' }}
                                    >
                                        Segna tutte come lette
                                    </button>
                                </div>
                            </div>
                        )}
                        {reviews.map((review) => (
                            <CardReview
                                key={review.id}
                                id={review.id}
                                publicReview={review.public_review}
                                submittedAt={review.submitted_at}
                                createdAt={review.created_at}
                                reviewerRole={review.reviewer_role}
                                ratings={review.ratings}
                                isRead={review.is_read}
                                onMarkAsRead={handleMarkAsRead}
                            />
                        ))}
                    </div>
                ) : (
                    <div className="text-center text-gray-500">
                        Nessuna recensione ancora
                    </div>
                )}
            </div>
        </MobilePageStart>
    )
}

export default ReviewsPage
