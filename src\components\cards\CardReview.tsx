import React from 'react';
import { format } from 'date-fns';

interface CardReviewProps {
    id: string;
    publicReview: string;
    submittedAt: string;
    reviewerRole: string;
    isRead?: boolean;
    onMarkAsRead?: (id: string) => Promise<void>;
}

const CardReview: React.FC<CardReviewProps> = ({
    id,
    publicReview,
    submittedAt,
    reviewerRole,
    isRead = true,
    onMarkAsRead
}) => {
    const handleMarkAsRead = async (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (onMarkAsRead) {
            await onMarkAsRead(id);
        }
    };

    // Format the date using date-fns
    const formattedDate = format(new Date(submittedAt), 'dd MMM yyyy');
    
    return (
        <div className={`w-full p-4 bg-white rounded-lg shadow-sm border-l-4 ${isRead ? 'border-transparent' : 'border-blue-500'}`}>
            <div className="flex justify-between items-center mb-2">
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-semibold capitalize text-blue-600">
                        {reviewerRole}
                    </span>
                    {!isRead && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Nuovo
                        </span>
                    )}
                </div>
                <span className="text-xs text-gray-500">
                    {formattedDate}
                </span>
            </div>
            <p className="text-sm text-gray-700">{publicReview}</p>
            
            {!isRead && onMarkAsRead && (
                <div className="mt-3 flex justify-end">
                    <button 
                        onClick={handleMarkAsRead}
                        className="text-xs bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-md transition-colors"
                    >
                        Segna come letto
                    </button>
                </div>
            )}
        </div>
    );
};

export default CardReview;
