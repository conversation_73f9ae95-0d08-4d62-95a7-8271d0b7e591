import React from 'react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { Star } from 'lucide-react';

interface CardReviewProps {
    id: string;
    publicReview: string;
    submittedAt: string;
    createdAt?: string;
    reviewerRole: string;
    ratings?: Record<string, any>;
    isRead?: boolean;
    onMarkAsRead?: (id: string) => Promise<void>;
}

const CardReview: React.FC<CardReviewProps> = ({
    id,
    publicReview,
    submittedAt,
    createdAt,
    reviewerRole,
    ratings,
    isRead = true,
    onMarkAsRead
}) => {
    const handleMarkAsRead = async (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (onMarkAsRead) {
            await onMarkAsRead(id);
        }
    };

    // Format the dates using date-fns with Italian locale
    const formattedSubmittedDate = format(new Date(submittedAt), 'dd MMM yyyy', { locale: it });
    const formattedCreatedDate = createdAt ? format(new Date(createdAt), 'dd MMM yyyy', { locale: it }) : null;

    // Extract overall rating if available
    const overallRating = ratings?.overall || ratings?.rating || null;

    // Render star rating
    const renderStars = (rating: number) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push(
                    <Star key={i} size={14} className="fill-yellow-400 text-yellow-400" />
                );
            } else if (i === fullStars && hasHalfStar) {
                stars.push(
                    <div key={i} className="relative">
                        <Star size={14} className="text-gray-300" />
                        <div className="absolute inset-0 overflow-hidden w-1/2">
                            <Star size={14} className="fill-yellow-400 text-yellow-400" />
                        </div>
                    </div>
                );
            } else {
                stars.push(
                    <Star key={i} size={14} className="text-gray-300" />
                );
            }
        }
        return stars;
    };

    return (
        <div className={`w-full p-4 bg-white rounded-lg shadow-sm border-l-4 ${isRead ? 'border-transparent' : 'border-[#113158]'}`}>
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center space-x-2">
                    <span className="text-sm font-semibold capitalize" style={{ color: '#113158' }}>
                        {reviewerRole}
                    </span>
                    {!isRead && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium" style={{ backgroundColor: '#fcbd4c', color: '#113158' }}>
                            Nuovo
                        </span>
                    )}
                </div>
                <div className="text-right">
                    <div className="text-xs text-gray-500">
                        Inviata: {formattedSubmittedDate}
                    </div>
                    {formattedCreatedDate && formattedCreatedDate !== formattedSubmittedDate && (
                        <div className="text-xs text-gray-400">
                            Creata: {formattedCreatedDate}
                        </div>
                    )}
                </div>
            </div>

            {/* Rating display */}
            {overallRating && (
                <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center space-x-1">
                        {renderStars(overallRating)}
                    </div>
                    <span className="text-sm text-gray-600">({overallRating}/5)</span>
                </div>
            )}

            <p className="text-sm text-gray-700 leading-relaxed">{publicReview}</p>

            {!isRead && onMarkAsRead && (
                <div className="mt-3 flex justify-end">
                    <button
                        onClick={handleMarkAsRead}
                        className="text-xs text-white py-1.5 px-4 rounded-md transition-colors hover:opacity-90"
                        style={{ backgroundColor: '#113158' }}
                    >
                        Segna come letto
                    </button>
                </div>
            )}
        </div>
    );
};

export default CardReview;
